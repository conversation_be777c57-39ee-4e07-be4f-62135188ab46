# 📱 One-Tap iPhone Shortcuts

## 🚀 Quick Photo Upload Shortcut

### Simple Setup (5 minutes):

1. **Open Shortcuts app** on your iPhone
2. **Tap "+" to create new shortcut**
3. **Add these actions in exact order:**

---

### Action 1: Select Photos
- Search: "Select Photos"
- Enable: "Select Multiple" 
- Set limit: 10

### Action 2: Text
- Search: "Text"
- Type: `http://**************:5000/upload/image`

### Action 3: Repeat with Each
- Search: "Repeat with Each"
- Input: "Photos" (from Action 1)

### Action 4: Encode Media (Inside the loop)
- Search: "Encode Media" 
- Format: "Base64"
- Input: "Item from Repeat" (current photo)

### Action 5: Get Contents of URL (Inside the loop)
- Search: "Get Contents of URL"
- URL: "Text" (from Action 2)
- Method: "POST"
- Headers: 
  - Key: `Content-Type`
  - Value: `application/json`
- Request Body: **JSON** (not Text!)
- Add key-value pair:
  - Key: `image`
  - Value: Select "Encoded Media" (from Action 4)

### Action 6: Show Notification
- Search: "Show Notification"
- Title: "Photo Uploaded!"
- Body: "Successfully sent to server"

---

## 🎯 One-Tap Usage:
1. Run the shortcut
2. Select photos (up to 10)
3. Tap "Done"
4. Wait for "Photo Uploaded!" notification

## 📋 Copy-Paste Shortcut URL:
```
shortcuts://create-shortcut?name=Send%20Photos&actions=%5B%7B%22WFWorkflowActionIdentifier%22%3A%22is.workflow.actions.documentpicker.open%22%2C%22WFWorkflowActionParameters%22%3A%7B%22WFDocumentPickerMode%22%3A%22Select%22%2C%22WFDocumentPickerSelectMultiple%22%3Atrue%7D%7D%5D
```

## 🔧 Troubleshooting:
- **"Conversion Error"**: Make sure Request Body is set to "JSON" and use key-value pairs
- **"Can't connect"**: Check server is running and firewall is off
- **"Permission denied"**: Allow Photos access in Settings > Privacy

## 🏠 Add to Home Screen:
1. Edit shortcut → Settings (⚙️)
2. "Add to Home Screen"
3. Choose icon and name
4. Tap "Add"

Now you have one-tap photo uploading! 📸✨
