{"WFWorkflowMinimumClientVersionString": "900", "WFWorkflowMinimumClientVersion": 900, "WFWorkflowIcon": {"WFWorkflowIconStartColor": 431817727, "WFWorkflowIconGlyphNumber": 61447}, "WFWorkflowClientVersion": "2605.0.5", "WFWorkflowOutputContentItemClasses": [], "WFWorkflowHasOutputFallback": false, "WFWorkflowActions": [{"WFWorkflowActionIdentifier": "is.workflow.actions.contacts", "WFWorkflowActionParameters": {"WFContactHandleMultipleSelection": true}}, {"WFWorkflowActionIdentifier": "is.workflow.actions.properties.contacts", "WFWorkflowActionParameters": {"WFContentItemPropertyName": "Contact Details", "WFInput": {"Value": {"OutputUUID": "1A2B3C4D-5E6F-7890-ABCD-EF1234567890", "Type": "ActionOutput", "OutputName": "Contacts"}, "WFSerializationType": "WFTextTokenAttachment"}}}, {"WFWorkflowActionIdentifier": "is.workflow.actions.gettext", "WFWorkflowActionParameters": {"WFTextActionText": "http://**************:5000/upload/contacts"}}, {"WFWorkflowActionIdentifier": "is.workflow.actions.downloadurl", "WFWorkflowActionParameters": {"WFHTTPMethod": "POST", "WFURL": {"Value": {"OutputUUID": "3A4B5C6D-7E8F-9012-CDEF-************", "Type": "ActionOutput", "OutputName": "Text"}, "WFSerializationType": "WFTextTokenAttachment"}, "WFHTTPHeaders": {"Content-Type": "application/json"}, "WFHTTPBodyType": "JSON", "WFJSONValues": {"contacts": {"Value": {"OutputUUID": "2A3B4C5D-6E7F-8901-BCDE-F23456789012", "Type": "ActionOutput", "OutputName": "Contact Details"}, "WFSerializationType": "WFTextTokenAttachment"}}}}, {"WFWorkflowActionIdentifier": "is.workflow.actions.shownotification", "WFWorkflowActionParameters": {"WFNotificationActionTitle": "Contacts Uploaded! 📞", "WFNotificationActionBody": "Successfully sent contacts to server"}}], "WFWorkflowInputContentItemClasses": ["WFContactContentItem"], "WFWorkflowImportQuestions": [], "WFWorkflowTypes": [], "WFQuickActionSurfaces": [], "WFWorkflowHasShortcutInputVariables": false}