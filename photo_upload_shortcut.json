{"WFWorkflowMinimumClientVersionString": "900", "WFWorkflowMinimumClientVersion": 900, "WFWorkflowIcon": {"WFWorkflowIconStartColor": 2071128575, "WFWorkflowIconGlyphNumber": 61440}, "WFWorkflowClientVersion": "2605.0.5", "WFWorkflowOutputContentItemClasses": [], "WFWorkflowHasOutputFallback": false, "WFWorkflowActions": [{"WFWorkflowActionIdentifier": "is.workflow.actions.documentpicker.open", "WFWorkflowActionParameters": {"WFDocumentPickerMode": "Select", "WFDocumentPickerSelectMultiple": true, "WFDocumentPickerFileTypes": ["public.image"], "WFDocumentPickerSelectLimit": 10}}, {"WFWorkflowActionIdentifier": "is.workflow.actions.gettext", "WFWorkflowActionParameters": {"WFTextActionText": "http://**************:5000/upload/image"}}, {"WFWorkflowActionIdentifier": "is.workflow.actions.repeat.each", "WFWorkflowActionParameters": {"WFInput": {"Value": {"OutputUUID": "1A2B3C4D-5E6F-7890-ABCD-EF1234567890", "Type": "ActionOutput", "OutputName": "Files"}, "WFSerializationType": "WFTextTokenAttachment"}, "GroupingIdentifier": "REPEAT_LOOP_1"}}, {"WFWorkflowActionIdentifier": "is.workflow.actions.base64encode", "WFWorkflowActionParameters": {"WFInput": {"Value": {"OutputUUID": "REPEAT_ITEM_1", "Type": "ActionOutput", "OutputName": "<PERSON><PERSON> from Repeat"}, "WFSerializationType": "WFTextTokenAttachment"}}}, {"WFWorkflowActionIdentifier": "is.workflow.actions.downloadurl", "WFWorkflowActionParameters": {"WFHTTPMethod": "POST", "WFURL": {"Value": {"OutputUUID": "2A3B4C5D-6E7F-8901-BCDE-F23456789012", "Type": "ActionOutput", "OutputName": "Text"}, "WFSerializationType": "WFTextTokenAttachment"}, "WFHTTPHeaders": {"Content-Type": "application/json"}, "WFHTTPBodyType": "JSON", "WFJSONValues": {"image": {"Value": {"OutputUUID": "3A4B5C6D-7E8F-9012-CDEF-************", "Type": "ActionOutput", "OutputName": "Base64 Encoded"}, "WFSerializationType": "WFTextTokenAttachment"}}}}, {"WFWorkflowActionIdentifier": "is.workflow.actions.repeat.each", "WFWorkflowActionParameters": {"GroupingIdentifier": "REPEAT_LOOP_1", "WFControlFlowMode": 1}}, {"WFWorkflowActionIdentifier": "is.workflow.actions.shownotification", "WFWorkflowActionParameters": {"WFNotificationActionTitle": "Photos Uploaded! 📸", "WFNotificationActionBody": "Successfully sent photos to server"}}], "WFWorkflowInputContentItemClasses": ["WFAppEntityContentItem", "WFMPMediaContentItem", "WFAVAssetContentItem", "WFPDFContentItem", "WFPhotoMediaContentItem", "WFVideoMediaContentItem", "WFGenericFileContentItem"], "WFWorkflowImportQuestions": [], "WFWorkflowTypes": [], "WFQuickActionSurfaces": [], "WFWorkflowHasShortcutInputVariables": false}